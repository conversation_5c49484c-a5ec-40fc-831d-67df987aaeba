package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.entity.Document;
import com.linkapi.service.DocumentService;
import com.linkapi.service.ExcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel上传控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class UploadController {

    private final ExcelService excelService;
    private final DocumentService documentService;

    /**
     * 上传Excel文件并解析为单据
     */
    @PostMapping("/excel")
    public Result<Map<String, Object>> uploadExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        try {
            log.info("开始处理Excel文件: {}", file.getOriginalFilename());
            
            // 解析Excel文件为单据列表
            List<Document> documents = excelService.parseExcelToDocuments(file);
            
            // 保存单据到数据库
            int successCount = 0;
            int errorCount = 0;
            
            for (Document document : documents) {
                try {
                    documentService.createDocument(document);
                    successCount++;
                } catch (Exception e) {
                    log.error("保存单据失败: {}", document.getDocumentNo(), e);
                    errorCount++;
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Excel文件处理完成");
            result.put("totalRows", documents.size());
            result.put("successRows", successCount);
            result.put("errorRows", errorCount);
            result.put("documentCount", successCount);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            
            log.info("Excel文件处理完成: 总行数={}, 成功={}, 失败={}", 
                documents.size(), successCount, errorCount);
            
            return Result.success("Excel文件上传成功", result);
            
        } catch (Exception e) {
            log.error("Excel文件处理失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Excel文件处理失败: " + e.getMessage());
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            
            return Result.error("Excel文件处理失败");
        }
    }

    /**
     * 获取Excel模板
     */
    @GetMapping("/template")
    public Result<String> getTemplate() {
        // TODO: 实现Excel模板下载功能
        return Result.success("模板下载功能待实现");
    }

    /**
     * 获取上传历史
     */
    @GetMapping("/history")
    public Result<Map<String, Object>> getUploadHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        // TODO: 实现上传历史查询功能
        Map<String, Object> result = new HashMap<>();
        result.put("records", new Object[0]);
        result.put("total", 0);
        result.put("page", page);
        result.put("size", size);
        
        return Result.success("查询成功", result);
    }

    /**
     * 获取上传结果
     */
    @GetMapping("/result/{id}")
    public Result<Map<String, Object>> getUploadResult(@PathVariable String id) {
        // TODO: 实现上传结果查询功能
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("status", "SUCCESS");
        
        return Result.success("查询成功", result);
    }

    /**
     * 删除上传记录
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUploadRecord(@PathVariable String id) {
        // TODO: 实现删除上传记录功能
        return Result.success("删除成功");
    }

    /**
     * 重新处理上传文件
     */
    @PostMapping("/{id}/reprocess")
    public Result<String> reprocessUpload(@PathVariable String id) {
        // TODO: 实现重新处理功能
        return Result.success("重新处理成功");
    }

    /**
     * 获取支持的文件类型
     */
    @GetMapping("/supported-types")
    public Result<String[]> getSupportedFileTypes() {
        String[] types = {".xlsx", ".xls"};
        return Result.success("查询成功", types);
    }

    /**
     * 获取上传配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("maxFileSize", "10MB");
        config.put("supportedTypes", new String[]{".xlsx", ".xls"});
        config.put("maxRows", 5000);
        
        return Result.success("查询成功", config);
    }
}