package com.linkapi.controller;

import com.linkapi.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "Link API 后端服务运行正常");
        
        return Result.success("服务正常", data);
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("application", "Link API Backend");
        data.put("version", "1.0.0");
        data.put("java.version", System.getProperty("java.version"));
        data.put("spring.profiles.active", System.getProperty("spring.profiles.active", "default"));
        data.put("timestamp", LocalDateTime.now());
        
        return Result.success("系统信息", data);
    }
}
