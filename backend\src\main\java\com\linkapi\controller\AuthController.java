package com.linkapi.controller;

import com.linkapi.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginData) {
        String username = loginData.get("username");
        String password = loginData.get("password");
        
        log.info("用户登录请求: username={}", username);
        
        // 简单的模拟登录验证
        if ("admin".equals(username) && "123456".equals(password)) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", "mock-jwt-token-" + System.currentTimeMillis());
            data.put("username", username);
            data.put("nickname", "管理员");
            data.put("avatar", "");
            data.put("roles", new String[]{"admin"});
            data.put("permissions", new String[]{"*:*:*"});
            data.put("loginTime", LocalDateTime.now());
            
            return Result.success("登录成功", data);
        } else {
            return Result.error("用户名或密码错误");
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/getInfo")
    public Result<Map<String, Object>> getInfo() {
        Map<String, Object> data = new HashMap<>();
        data.put("username", "admin");
        data.put("nickname", "管理员");
        data.put("avatar", "");
        data.put("roles", new String[]{"admin"});
        data.put("permissions", new String[]{"*:*:*"});
        
        return Result.success("获取用户信息成功", data);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        log.info("用户登出");
        return Result.success("登出成功", "success");
    }

    /**
     * 获取验证码
     */
    @GetMapping("/captchaImage")
    public Result<Map<String, Object>> getCaptcha() {
        Map<String, Object> data = new HashMap<>();
        data.put("uuid", "mock-uuid-" + System.currentTimeMillis());
        data.put("img", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
        data.put("captchaEnabled", false);
        
        return Result.success("获取验证码成功", data);
    }
}
